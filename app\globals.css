@import "tailwindcss";

:root {
  --background: #0a0a0a;
  --foreground: #ededed;
  --sidebar-bg: #1a1a1a;
  --card-bg: #262626;
  --border-color: #404040;
  --input-bg: #1a1a1a;
  --button-bg: #2563eb;
  --button-hover: #1d4ed8;
}



body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
  margin: 0;
  padding: 0;
}

/* Input styling */
input {
  background: var(--input-bg);
  color: var(--foreground);
  border: 1px solid var(--border-color);
  padding: 8px 12px;
  border-radius: 4px;
}

input:focus {
  outline: none;
  border-color: var(--button-bg);
}

/* Button styling */
button {
  background: var(--button-bg);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}

button:hover:not(:disabled) {
  background: var(--button-hover);
}

button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Link styling */
a {
  color: var(--foreground);
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

/* Layout components */
.app-container {
  display: flex;
  min-height: 100vh;
}

.sidebar {
  width: 250px;
  background: var(--sidebar-bg);
  padding: 24px;
  border-right: 1px solid var(--border-color);
}

.sidebar h2 {
  margin-top: 0;
  margin-bottom: 16px;
  color: var(--foreground);
}

.sidebar ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sidebar li {
  margin-bottom: 8px;
}

.main-content {
  flex: 1;
  padding: 32px;
}

.main-content h1 {
  margin-top: 0;
  margin-bottom: 32px;
  color: var(--foreground);
}

.input-section {
  margin-bottom: 24px;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.input-field {
  min-width: 200px;
}

.section {
  margin-bottom: 32px;
}

.section h2 {
  margin-bottom: 16px;
  color: var(--foreground);
}

.content-box {
  background: var(--card-bg);
  padding: 16px;
  border-radius: 8px;
  border: 1px solid var(--border-color);
  min-height: 80px;
  color: var(--foreground);
}

.code-block {
  background: var(--card-bg);
  padding: 16px;
  border-radius: 8px;
  border: 1px solid var(--border-color);
  color: var(--foreground);
  overflow-x: auto;
}

.chat-history {
  max-height: 300px;
  overflow-y: auto;
}

.chat-message {
  margin-bottom: 8px;
  padding: 8px 0;
  border-bottom: 1px solid var(--border-color);
}

.chat-message:last-child {
  border-bottom: none;
}

.chat-role {
  font-weight: bold;
  color: var(--button-bg);
}

.search-list {
  background: var(--card-bg);
  padding: 16px;
  border-radius: 8px;
  border: 1px solid var(--border-color);
  margin: 0;
}

.search-list li {
  margin-bottom: 8px;
  color: var(--foreground);
}

.search-list li:last-child {
  margin-bottom: 0;
}
