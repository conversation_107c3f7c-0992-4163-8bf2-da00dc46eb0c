import { NextRequest, NextResponse } from 'next/server';
import { readAgentState, writeAgentState } from '../../lib/agentState';
import { ollama } from 'ollama-ai-provider';
import { generateText, streamText } from 'ai';
import { createOpenRouter } from '@openrouter/ai-sdk-provider';

const openrouter = createOpenRouter({
  apiKey: '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',
});

export async function GET() {
  const state = readAgentState();
  return NextResponse.json(state);
}

export async function POST(req: NextRequest) {
  const { startingPoint, extraRequest } = await req.json();
  let state = readAgentState();

  // Stage 1: Query Generation using Ollama (gemma3:12b)
  let generatedQuery = '';
  if (startingPoint) {
    const ollamaRes = await generateText({
      model: ollama('gemma3:12b'),
      prompt: `Generate an advanced Google search query for: ${startingPoint}`,
    });
    generatedQuery = ollamaRes.text;
  } else {
    generatedQuery = 'No starting point provided.';
  }

  // Simulate search results (placeholder for now)
  const searchResults = [
    `Result 1 for: ${generatedQuery}`,
    `Result 2 for: ${generatedQuery}`,
  ];

  // Stage 2: Analysis and Agent Chat using OpenRouter (deepseek/deepseek-r1-0528:free)
  // Agent 1: Non-thinking agent
  const agent1Prompt = `You are Agent 1 (Non-thinking). Here is the research context: ${searchResults.join(' | ')}. What do you think are the key points?`;
  const agent1Res = await streamText({
    model: openrouter.chat('deepseek/deepseek-r1-0528:free'),
    messages: [
      { role: 'system', content: 'You are Agent 1 (Non-thinking).' },
      { role: 'user', content: agent1Prompt },
    ],
  });
  const agent1Text = await agent1Res.text;

  // Agent 2: Thinking agent
  const agent2Prompt = `You are Agent 2 (Thinking). Here is what Agent 1 said: "${agent1Text}". Analyze and provide deeper insights or next steps.`;
  const agent2Res = await streamText({
    model: openrouter.chat('deepseek/deepseek-r1-0528:free'),
    messages: [
      { role: 'system', content: 'You are Agent 2 (Thinking).' },
      { role: 'user', content: agent2Prompt },
    ],
  });
  const agent2Text = await agent2Res.text;

  // Update state
  state = {
    ...state,
    stage: 'Stage 2: Analysis',
    headings: [
      { id: 'query', title: 'Generated Query' },
      { id: 'results', title: 'Search Results' },
      { id: 'agent1', title: 'Agent 1 (Non-thinking) Response' },
      { id: 'agent2', title: 'Agent 2 (Thinking) Response' },
    ],
    chat: [
      { role: 'user', content: startingPoint },
      { role: 'agent', content: generatedQuery },
      { role: 'agent1', content: agent1Text },
      { role: 'agent2', content: agent2Text },
    ],
    scratchpad: `${searchResults.join('\n')}`,
    searchList: [generatedQuery],
    extraRequest,
  };
  writeAgentState(state);
  return NextResponse.json(state);
} 