'use client';

import { useState, useEffect } from 'react';

export default function Home() {
  const [agentState, setAgentState] = useState<any>(null);
  const [startingPoint, setStartingPoint] = useState('');
  const [extraRequest, setExtraRequest] = useState('');
  const [loading, setLoading] = useState(false);

  // Fetch agent state from API
  useEffect(() => {
    console.log('[Frontend] Component mounted, fetching initial agent state');
    fetch('/api/agent')
      .then(res => {
        console.log('[Frontend] Initial state fetch response received:', res.status);
        return res.json();
      })
      .then(data => {
        console.log('[Frontend] Initial agent state loaded:', { stage: data.stage, hasChat: !!data.chat?.length });
        setAgentState(data);
      })
      .catch(error => {
        console.error('[Frontend] Error fetching initial agent state:', error);
      });
  }, []);

  // Log agent state changes
  useEffect(() => {
    if (agentState) {
      console.log('[Frontend] Agent state updated:', {
        stage: agentState.stage,
        chatMessages: agentState.chat?.length || 0,
        headings: agentState.headings?.length || 0,
        searchListItems: agentState.searchList?.length || 0,
        hasScratchpad: !!agentState.scratchpad,
        hasExtraRequest: !!agentState.extraRequest
      });
    }
  }, [agentState]);

  const handleStart = async () => {
    console.log('[Frontend] Starting research process with:', { startingPoint, extraRequest });
    setLoading(true);

    try {
      console.log('[Frontend] Sending POST request to /api/agent');
      const res = await fetch('/api/agent', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ startingPoint, extraRequest })
      });

      console.log('[Frontend] API response received:', res.status);

      if (!res.ok) {
        throw new Error(`API request failed with status: ${res.status}`);
      }

      const data = await res.json();
      console.log('[Frontend] Research process completed, updating state:', {
        stage: data.stage,
        chatLength: data.chat?.length,
        hasError: !!data.error
      });

      if (data.error) {
        console.error('[Frontend] API returned error:', data.error);
      }

      setAgentState(data);
    } catch (error) {
      console.error('[Frontend] Error during research process:', error);
      // You might want to show an error message to the user here
    } finally {
      console.log('[Frontend] Research process finished, setting loading to false');
      setLoading(false);
    }
  };

  return (
    <div className="app-container">
      {/* Sidebar for headings */}
      <aside className="sidebar">
        <h2>Sections</h2>
        <ul>
          {agentState?.headings?.map((h: any, i: number) => (
            <li key={i}><a href={`#${h.id}`}>{h.title}</a></li>
          ))}
        </ul>
      </aside>
      <main className="main-content">
        <h1>Research Agent Dashboard</h1>
        <div className="input-section">
          <input
            type="text"
            placeholder="Enter starting point..."
            value={startingPoint}
            onChange={e => {
              console.log('[Frontend] Starting point changed:', e.target.value);
              setStartingPoint(e.target.value);
            }}
            className="input-field"
          />
          <input
            type="text"
            placeholder="Extra request (optional)"
            value={extraRequest}
            onChange={e => {
              console.log('[Frontend] Extra request changed:', e.target.value);
              setExtraRequest(e.target.value);
            }}
            className="input-field"
          />
          <button
            onClick={() => {
              console.log('[Frontend] Start/Update button clicked');
              handleStart();
            }}
            disabled={loading}
          >
            {loading ? 'Processing...' : 'Start/Update'}
          </button>
        </div>
        <section className="section">
          <h2>Current Stage</h2>
          <div className="content-box">
            {agentState?.stage || 'Not started'}
          </div>
        </section>
        <section className="section">
          <h2>Agent State</h2>
          <pre className="code-block">
            {JSON.stringify(agentState, null, 2)}
          </pre>
        </section>
        <section className="section">
          <h2>Chat History</h2>
          <div className="content-box chat-history">
            {agentState?.chat?.map((msg: any, i: number) => (
              <div key={i} className="chat-message">
                <span className="chat-role">{msg.role}:</span> {msg.content}
              </div>
            )) || 'No chat yet.'}
          </div>
        </section>
        <section className="section">
          <h2>Research Context (Scratchpad)</h2>
          <div className="content-box">
            {agentState?.scratchpad || 'No context yet.'}
          </div>
        </section>
        <section className="section">
          <h2>Search List</h2>
          <ul className="search-list">
            {agentState?.searchList?.map((item: string, i: number) => (
              <li key={i}>{item}</li>
            )) || <li>No search items yet.</li>}
          </ul>
        </section>
      </main>
    </div>
  );
}