'use client';

import { useState, useEffect } from 'react';

export default function Home() {
  const [agentState, setAgentState] = useState<any>(null);
  const [startingPoint, setStartingPoint] = useState('');
  const [extraRequest, setExtraRequest] = useState('');
  const [loading, setLoading] = useState(false);

  // Fetch agent state from API
  useEffect(() => {
    fetch('/api/agent')
      .then(res => res.json())
      .then(setAgentState);
  }, []);

  const handleStart = async () => {
    setLoading(true);
    const res = await fetch('/api/agent', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ startingPoint, extraRequest })
    });
    const data = await res.json();
    setAgentState(data);
    setLoading(false);
  };

  return (
    <div className="app-container">
      {/* Sidebar for headings */}
      <aside className="sidebar">
        <h2>Sections</h2>
        <ul>
          {agentState?.headings?.map((h: any, i: number) => (
            <li key={i}><a href={`#${h.id}`}>{h.title}</a></li>
          ))}
        </ul>
      </aside>
      <main className="main-content">
        <h1>Research Agent Dashboard</h1>
        <div className="input-section">
          <input
            type="text"
            placeholder="Enter starting point..."
            value={startingPoint}
            onChange={e => setStartingPoint(e.target.value)}
            className="input-field"
          />
          <input
            type="text"
            placeholder="Extra request (optional)"
            value={extraRequest}
            onChange={e => setExtraRequest(e.target.value)}
            className="input-field"
          />
          <button onClick={handleStart} disabled={loading}>
            {loading ? 'Processing...' : 'Start/Update'}
          </button>
        </div>
        <section className="section">
          <h2>Current Stage</h2>
          <div className="content-box">
            {agentState?.stage || 'Not started'}
          </div>
        </section>
        <section className="section">
          <h2>Agent State</h2>
          <pre className="code-block">
            {JSON.stringify(agentState, null, 2)}
          </pre>
        </section>
        <section className="section">
          <h2>Chat History</h2>
          <div className="content-box chat-history">
            {agentState?.chat?.map((msg: any, i: number) => (
              <div key={i} className="chat-message">
                <span className="chat-role">{msg.role}:</span> {msg.content}
              </div>
            )) || 'No chat yet.'}
          </div>
        </section>
        <section className="section">
          <h2>Research Context (Scratchpad)</h2>
          <div className="content-box">
            {agentState?.scratchpad || 'No context yet.'}
          </div>
        </section>
        <section className="section">
          <h2>Search List</h2>
          <ul className="search-list">
            {agentState?.searchList?.map((item: string, i: number) => (
              <li key={i}>{item}</li>
            )) || <li>No search items yet.</li>}
          </ul>
        </section>
      </main>
    </div>
  );
}