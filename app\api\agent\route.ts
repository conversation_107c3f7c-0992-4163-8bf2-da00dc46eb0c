import { NextRequest, NextResponse } from 'next/server';
import { readAgentState, writeAgentState } from '../../../lib/agentState';
import { generateObject, streamText } from 'ai';
import { createOpenRouter } from '@openrouter/ai-sdk-provider';
import { z } from 'zod';
import { createOpenAI } from '@ai-sdk/openai';
import FirecrawlApp from '@mendable/firecrawl-js';

const openai = createOpenAI({
    apiKey: '********************************************************************************************************************************************************************',
});

const openrouter = createOpenRouter({
  apiKey: '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',
});

const firecrawl = new FirecrawlApp({ apiKey: 'not-needed', apiUrl: 'http://localhost:3002' });

export async function GET() {
  const state = readAgentState();
  return NextResponse.json(state);
}

export async function POST(req: NextRequest) {
  try {
    const { startingPoint, extraRequest } = await req.json();
    let state = readAgentState();

    // Stage 1: Query Generation using OpenAI (gpt-4o-mini)
    let generatedQuery = '';
    if (startingPoint) {
      const ollamaRes = await generateObject({
        model: openai('gpt-4o-mini'),
        schema: z.object({ query: z.string() }),
        prompt: `Generate an advanced Google search query for: ${startingPoint}`,
      });
      generatedQuery = ollamaRes.object.query;
    } else {
      generatedQuery = 'No starting point provided.';
    }

    // Stage 1.5: Search and scrape using Firecrawl
    let searchResults: any[] = [];
    let firecrawlContext = '';
    if (generatedQuery && generatedQuery !== 'No starting point provided.') {
      const firecrawlResult = await firecrawl.search(generatedQuery, {
        limit: 3,
        scrapeOptions: {
          formats: ['markdown', 'links']
        }
      });
      searchResults = firecrawlResult.data.map((result: any) => ({
        title: result.title,
        url: result.url,
        markdown: result.markdown,
        links: result.links
      }));
      firecrawlContext = searchResults.map(r => `Title: ${r.title}\nURL: ${r.url}\nContent: ${r.markdown?.substring(0, 300)}...\nLinks: ${(r.links || []).slice(0, 3).join(', ')}...`).join('\n\n');
    } else {
      firecrawlContext = 'No search performed.';
    }

    // Stage 2: Analysis and Agent Chat using OpenRouter (deepseek/deepseek-r1-0528:free)
    // Agent 1: Non-thinking agent
    const agent1Prompt = `You are Agent 1 (Non-thinking). Here is the research context: ${firecrawlContext}. What do you think are the key points?`;
    const agent1Res = await streamText({
      model: openrouter.chat('deepseek/deepseek-r1-0528:free'),
      messages: [
        { role: 'system', content: 'You are Agent 1 (Non-thinking).' },
        { role: 'user', content: agent1Prompt },
      ],
    });
    const agent1Text = await agent1Res.text;

    // Agent 2: Thinking agent
    const agent2Prompt = `You are Agent 2 (Thinking). Here is what Agent 1 said: "${agent1Text}". Analyze and provide deeper insights or next steps.`;
    const agent2Res = await streamText({
      model: openrouter.chat('deepseek/deepseek-r1-0528:free'),
      messages: [
        { role: 'system', content: 'You are Agent 2 (Thinking).' },
        { role: 'user', content: agent2Prompt },
      ],
    });
    const agent2Text = await agent2Res.text;

    // Update state
    state = {
      ...state,
      stage: 'Stage 2: Analysis',
      headings: [
        { id: 'query', title: 'Generated Query' },
        { id: 'results', title: 'Search Results' },
        { id: 'agent1', title: 'Agent 1 (Non-thinking) Response' },
        { id: 'agent2', title: 'Agent 2 (Thinking) Response' },
      ],
      chat: [
        { role: 'user', content: startingPoint },
        { role: 'agent', content: generatedQuery },
        { role: 'agent1', content: agent1Text },
        { role: 'agent2', content: agent2Text },
      ],
      scratchpad: firecrawlContext,
      searchList: [generatedQuery],
      extraRequest,
      searchResults,
    };
    writeAgentState(state);
    return NextResponse.json(state);
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
} 