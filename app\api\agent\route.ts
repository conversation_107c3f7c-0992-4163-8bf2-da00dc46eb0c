// NOTE: Make sure to install the 'openai' package: npm install openai
import { NextRequest, NextResponse } from 'next/server';
import { readAgentState, writeAgentState } from '../../../lib/agentState';
import { streamText } from 'ai';
import { createOpenRouter } from '@openrouter/ai-sdk-provider';
import FirecrawlApp from '@mendable/firecrawl-js';
import OpenAI from 'openai';
import { ollama } from 'ollama-ai-provider';

const openrouter = createOpenRouter({
  apiKey: '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',
});
const firecrawl = new FirecrawlApp({ apiKey: 'not-needed', apiUrl: 'http://localhost:3002' });
const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });

export async function GET() {
  const state = readAgentState();
  return NextResponse.json(state);
}

export async function POST(req: NextRequest) {
  try {
    const { startingPoint, extraRequest } = await req.json();
    let state = readAgentState();

    // 1. Use OpenAI to get relevant links for the query
    let links: string[] = [];
    let openaiLinksText = '';
    if (startingPoint) {
      const openaiResponse = await openai.responses.create({
        model: 'gpt-4.1',
        input: [
          {
            role: 'user',
            content: [
              {
                type: 'input_text',
                text: `give me the links for resources related to the query: ${startingPoint}.
just give me the links which are the most relevant use multiple searches to gather a vast context around the query plus arrange the links and give me the most relevant ones at top 3 and below them other related ones that can be used for more context.`
              }
            ]
          },
          {
            id: 'ws_68452295a22081a38120ae1aa61dee41019c006be99371d6',
            type: 'web_search_call',
            status: 'completed'
          }
        ],
        text: { format: { type: 'text' } },
        reasoning: {},
        tools: [
          {
            type: 'web_search_preview',
            user_location: { type: 'approximate' },
            search_context_size: 'high'
          }
        ],
        temperature: 1,
        max_output_tokens: 2048,
        top_p: 1,
        store: true
      });
      // Extract links from the OpenAI response
      const outputText = openaiResponse.choices?.[0]?.message?.content?.[0]?.text || openaiResponse.choices?.[0]?.message?.content || '';
      openaiLinksText = outputText;
      // Use regex to extract all links
      links = Array.from(outputText.matchAll(/https?:\/\/[\w\-\.\/?#&=:%]+/g) as Iterable<RegExpMatchArray>).map(m => m[0]);
    }

    // 2. Scrape the top 3 links using Firecrawl, fallback to more if any fail
    let scrapedResults: any[] = [];
    let scrapeErrors: string[] = [];
    let usedLinks: string[] = [];
    if (links.length > 0) {
      let toTry = links.slice(0, 6); // Try up to 6 links to get at least 3 good ones
      for (let i = 0; i < toTry.length && scrapedResults.length < 3; i++) {
        try {
          const result = await firecrawl.scrapeUrl(toTry[i], { formats: ['markdown', 'links'] });
          if (result && result.success) {
            scrapedResults.push({
              title: result.title,
              url: result.url,
              markdown: result.markdown,
              links: result.links
            });
            usedLinks.push(toTry[i]);
          } else {
            scrapeErrors.push(`Failed to scrape: ${toTry[i]}`);
          }
        } catch (err) {
          scrapeErrors.push(`Error scraping ${toTry[i]}: ${err instanceof Error ? err.message : String(err)}`);
        }
      }
    }

    // 3. Use Ollama to generate a summary of the combined content
    let ollamaSummary = '';
    if (scrapedResults.length > 0) {
      const combinedMarkdown = scrapedResults.map(r => r.markdown).join('\n\n');
      // Use Ollama to summarize
      const ollamaRes = await fetch('http://localhost:11434/api/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          model: 'gemma3:12b',
          prompt: `Summarize the following research context for the query: ${startingPoint}\n\n${combinedMarkdown}`
        })
      });
      const ollamaData = await ollamaRes.json();
      ollamaSummary = ollamaData.response || ollamaData.text || '';
    } else {
      ollamaSummary = 'No content could be scraped for summary.';
    }

    // 4. Pass the summary to the agent chat stage
    const firecrawlContext = ollamaSummary;
    // Agent 1: Non-thinking agent
    const agent1Prompt = `You are Agent 1 (Non-thinking). Here is the research context: ${firecrawlContext}. What do you think are the key points?`;
    const agent1Res = await streamText({
      model: openrouter.chat('deepseek/deepseek-r1-0528:free'),
      messages: [
        { role: 'system', content: 'You are Agent 1 (Non-thinking).' },
        { role: 'user', content: agent1Prompt },
      ],
    });
    const agent1Text = await agent1Res.text;

    // Agent 2: Thinking agent
    const agent2Prompt = `You are Agent 2 (Thinking). Here is what Agent 1 said: "${agent1Text}". Analyze and provide deeper insights or next steps.`;
    const agent2Res = await streamText({
      model: openrouter.chat('deepseek/deepseek-r1-0528:free'),
      messages: [
        { role: 'system', content: 'You are Agent 2 (Thinking).' },
        { role: 'user', content: agent2Prompt },
      ],
    });
    const agent2Text = await agent2Res.text;

    // Update state
    state = {
      ...state,
      stage: 'Stage 2: Analysis',
      headings: [
        { id: 'query', title: 'Generated Query' },
        { id: 'results', title: 'Scraped Results' },
        { id: 'summary', title: 'Ollama Summary' },
        { id: 'agent1', title: 'Agent 1 (Non-thinking) Response' },
        { id: 'agent2', title: 'Agent 2 (Thinking) Response' },
      ],
      chat: [
        { role: 'user', content: startingPoint },
        { role: 'openai', content: openaiLinksText },
        { role: 'ollama', content: ollamaSummary },
        { role: 'agent1', content: agent1Text },
        { role: 'agent2', content: agent2Text },
      ],
      scratchpad: ollamaSummary,
      searchList: links,
      extraRequest,
      searchResults: scrapedResults,
      scrapeErrors,
      usedLinks,
    };
    writeAgentState(state);
    return NextResponse.json(state);
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}