import fs from 'fs';
import path from 'path';

const STATE_DIR = path.resolve(process.cwd(), 'agent-sessions');
const STATE_FILE = path.join(STATE_DIR, 'agentState.json');

export function ensureStateDir() {
  if (!fs.existsSync(STATE_DIR)) {
    fs.mkdirSync(STATE_DIR);
  }
}

export function readAgentState() {
  ensureStateDir();
  if (!fs.existsSync(STATE_FILE)) {
    return {
      stage: 'Not started',
      headings: [],
      chat: [],
      scratchpad: '',
      searchList: [],
    };
  }
  const data = fs.readFileSync(STATE_FILE, 'utf-8');
  return JSON.parse(data);
}

export function writeAgentState(state: any) {
  ensureStateDir();
  fs.writeFileSync(STATE_FILE, JSON.stringify(state, null, 2), 'utf-8');
} 